<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dashboard - mermantic</title>
  <link rel="stylesheet" href="/css/styles.css">
  <link rel="stylesheet" href="/nui/nui.css">
</head>
<body>
  <header>
    <div class="container">
      <h1>mermantic</h1>
      <nav>
        <ul>
          <li><a href="/">Home</a></li>
          <li><a href="/dashboard.html" class="active">Dashboard</a></li>
          <li><a href="#" id="logout-link">Logout</a></li>
        </ul>
      </nav>
    </div>
  </header>

  <main class="container">
    <section class="dashboard-header">
      <h2>My Diagrams</h2>
      <button id="create-new-chart" class="nui-button primary">Create New Diagram</button>
    </section>

    <!-- View Chart Modal -->
    <div id="view-chart-modal" class="modal" style="display: none;">
      <div class="modal-content">
        <div class="modal-header">
          <h3 id="view-chart-title">View Diagram</h3>
          <span class="close-modal" id="close-view-modal">&times;</span>
        </div>
        <div class="modal-body">
          <div id="view-chart-container" class="view-chart-container"></div>
        </div>
        <div class="modal-footer">
          <button id="view-edit-chart" class="nui-button small">Edit</button>
          <button id="view-delete-chart" class="nui-button small">Delete</button>
          <button id="view-share-chart" class="nui-button small">Share</button>
          <button id="view-fullscreen-chart" class="nui-button small">⛶ Fullscreen</button>
          <button id="close-view-button" class="nui-button secondary small">Close</button>
        </div>
      </div>
    </div>

    <section id="chart-editor" class="chart-editor" style="display: none;">
      <h3 id="editor-title">Create New Diagram</h3>
      <form id="chart-form">
        <input type="hidden" id="chart-id">
        <div class="form-group">
          <label for="chart-title">Title</label>
          <input type="text" id="chart-title" name="chart-title" class="nui-input" required>
        </div>

        <div class="form-group">
          <label for="chart-folder">Folder</label>
          <div class="folder-input-container">
            <input type="text" id="chart-folder" name="chart-folder" class="nui-input" placeholder="Enter folder name or leave empty">
            <select id="folder-suggestions" class="folder-suggestions nui-select">
              <option value="">Select existing folder...</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label for="chart-notes">Notes</label>
          <textarea id="chart-notes" name="chart-notes" class="nui-textarea" placeholder="Add notes or description for this diagram..." rows="3"></textarea>
        </div>

        <div class="form-group">
          <label for="chart-public">
            <input type="checkbox" id="chart-public" name="chart-public">
            Make diagram public
          </label>
        </div>

        <div class="editor-container">
          <div class="editor-panel">
            <h4>Mermaid Syntax <span id="syntax-indicator" class="syntax-indicator"></span>
              <button type="button" id="toggle-help" class="help-toggle" title="Show/Hide Help">?</button>
            </h4>
            <textarea id="chart-content" class="mermaid-editor" required>graph TD
    A[Start] --> B{Is it working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Debug]
    D --> B</textarea>

            <div id="help-panel" class="help-panel" style="display: none;">
              <h5>Quick Reference</h5>
              <div class="help-examples">
                <button type="button" class="help-example" data-example="flowchart">Flowchart</button>
                <button type="button" class="help-example" data-example="sequence">Sequence</button>
                <button type="button" class="help-example" data-example="gantt">Gantt</button>
                <button type="button" class="help-example" data-example="pie">Pie Chart</button>
              </div>
              <div class="help-shortcuts">
                <strong>Shortcuts:</strong><br>
                <kbd>Ctrl+S</kbd> Save • <kbd>Esc</kbd> Cancel • <kbd>Ctrl+Enter</kbd> Update Preview • <kbd>F11</kbd> Fullscreen
              </div>
              <div class="help-unicode">
                <strong>Unicode Characters:</strong><br>
                Some Unicode characters (emojis, special symbols) may be automatically replaced with compatible alternatives for better rendering.
              </div>
            </div>
          </div>
          <div class="preview-panel">
            <h4>Preview</h4>
            <div id="chart-preview" class="mermaid-preview"></div>
          </div>
        </div>

        <div class="form-actions">
          <button type="submit" class="nui-button primary">Save</button>
          <button type="button" id="toggle-fullscreen" class="nui-button secondary">⛶ Fullscreen</button>
          <button type="button" id="cancel-edit" class="nui-button secondary">Cancel</button>
        </div>
      </form>
    </section>

    <section id="charts-list" class="charts-list">
      <div id="no-charts-message" style="display: none;">
        <p>You don't have any diagrams yet. Create your first one!</p>
      </div>

      <div id="charts-grid" class="charts-grid">
        <!-- Charts will be loaded here dynamically -->
      </div>
    </section>
  </main>

  <footer>
    <div class="container">
      <p>&copy; 2023 mermantic. All rights reserved.</p>
    </div>
  </footer>

  <script src="/js/mermaid.min.js"></script>
  <script src="/nui/nui.js"></script>
  <script src="/js/auth.js"></script>
  <script src="/js/charts.js"></script>
  <script src="/js/zoom-controls.js"></script>
  <script>
    // Initialize Mermaid with better error handling
    mermaid.initialize({
      startOnLoad: false,
      securityLevel: 'loose',
      theme: 'default',
      logLevel: 'fatal', // Reduce console noise
      fontFamily: 'monospace',
      flowchart: {
        useMaxWidth: true,
        htmlLabels: true,
        curve: 'linear' // Use linear curves which are more reliable
      },
      er: {
        useMaxWidth: true
      },
      sequence: {
        useMaxWidth: true,
        wrap: true,
        showSequenceNumbers: false
      },
      gantt: {
        useMaxWidth: true
      },
      journey: {
        useMaxWidth: true
      },
      // Suppress errors to reduce console noise
      suppressErrors: true
    });

    document.addEventListener('DOMContentLoaded', function() {
      // Check if user is logged in via localStorage
      let user = JSON.parse(localStorage.getItem('user'));

      // If no user in localStorage, check if we're coming from Google auth
      if (!user) {
        console.log('No user in localStorage, checking session...');
        // Fetch current user from session
        fetchCurrentUser()
          .then(sessionUser => {
            if (sessionUser) {
              // Store user in localStorage
              localStorage.setItem('user', JSON.stringify(sessionUser));
              user = sessionUser;
              console.log('User found in session, stored in localStorage:', user);
              // Load user's charts
              loadUserCharts();
            } else {
              // No user in session either, redirect to login
              console.log('No user found in session, redirecting to login');
              window.location.href = '/login.html';
            }
          })
          .catch(error => {
            console.error('Error fetching current user:', error);
            window.location.href = '/login.html';
          });
      } else {
        // User already in localStorage, but verify session is still valid
        console.log('User found in localStorage:', user);
        console.log('Verifying session...');
        verifySession()
          .then(() => {
            // Session is valid, load charts
            console.log('Session verified successfully, loading charts');
            loadUserCharts();
          })
          .catch(error => {
            console.error('Error verifying session:', error);
            console.log('Session verification failed, but trying to load charts anyway');
            // Try to load charts anyway
            loadUserCharts();
          });
      }

      // Setup event listeners
      document.getElementById('create-new-chart').addEventListener('click', function() {
        showChartEditor(true);
      });
      document.getElementById('cancel-edit').addEventListener('click', hideChartEditor);

      // Fullscreen toggle
      document.getElementById('toggle-fullscreen').addEventListener('click', function() {
        const editor = document.getElementById('chart-editor');
        const button = this;

        if (editor.classList.contains('fullscreen')) {
          editor.classList.remove('fullscreen');
          button.textContent = '⛶ Fullscreen';
          document.body.style.overflow = '';
        } else {
          editor.classList.add('fullscreen');
          button.textContent = '⛶ Exit Fullscreen';
          document.body.style.overflow = 'hidden';
        }
      });

      // Add debounced input listener for preview updates and auto-save
      let previewUpdateTimeout;
      let autoSaveTimeout;
      document.getElementById('chart-content').addEventListener('input', function() {
        clearTimeout(previewUpdateTimeout);
        clearTimeout(autoSaveTimeout);
        previewUpdateTimeout = setTimeout(updateChartPreview, 500); // 500ms debounce
        autoSaveTimeout = setTimeout(autoSaveDraft, 2000); // Auto-save after 2 seconds
      });

      document.getElementById('chart-form').addEventListener('submit', saveChart);

      // Folder suggestions functionality
      document.getElementById('folder-suggestions').addEventListener('change', function() {
        if (this.value) {
          document.getElementById('chart-folder').value = this.value;
        }
      });

      // Load folders when showing editor
      loadFolders();

      // Help panel functionality
      document.getElementById('toggle-help').addEventListener('click', function() {
        const helpPanel = document.getElementById('help-panel');
        helpPanel.style.display = helpPanel.style.display === 'none' ? 'block' : 'none';
      });

      // Example templates
      const examples = {
        flowchart: `graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
    C --> E[End]
    D --> E`,
        sequence: `sequenceDiagram
    participant A as Alice
    participant B as Bob
    A->>B: Hello Bob!
    B-->>A: Hello Alice!
    A->>B: How are you?
    B-->>A: I'm good, thanks!`,
        gantt: `gantt
    title Project Timeline
    dateFormat YYYY-MM-DD
    section Planning
    Research    :done, research, 2024-01-01, 2024-01-07
    Design      :active, design, 2024-01-08, 2024-01-15
    section Development
    Coding      :coding, after design, 2024-01-30
    Testing     :testing, after coding, 2024-02-15`,
        pie: `pie title Favorite Programming Languages
    "JavaScript" : 35
    "Python" : 25
    "Java" : 20
    "C++" : 15
    "Other" : 5`
      };

      // Add event listeners to example buttons
      document.querySelectorAll('.help-example').forEach(button => {
        button.addEventListener('click', function() {
          const exampleType = this.getAttribute('data-example');
          const exampleCode = examples[exampleType];
          if (exampleCode) {
            document.getElementById('chart-content').value = exampleCode;
            updateChartPreview();
          }
        });
      });

      // Setup modal event listeners
      document.getElementById('close-view-modal').addEventListener('click', hideViewModal);
      document.getElementById('close-view-button').addEventListener('click', hideViewModal);
      document.getElementById('view-edit-chart').addEventListener('click', function() {
        const chartId = this.getAttribute('data-id');
        hideViewModal();
        editChart(chartId);
      });
      document.getElementById('view-delete-chart').addEventListener('click', function() {
        const chartId = this.getAttribute('data-id');
        hideViewModal();
        deleteChart(chartId);
      });
      document.getElementById('view-share-chart').addEventListener('click', function() {
        const shareId = this.getAttribute('data-id');
        shareChart(shareId);
      });
      document.getElementById('view-fullscreen-chart').addEventListener('click', function() {
        toggleViewFullscreen();
      });

      // Click outside modal in fullscreen to exit fullscreen (but not close modal)
      document.getElementById('view-chart-modal').addEventListener('click', function(e) {
        if (e.target === this && this.classList.contains('fullscreen')) {
          toggleViewFullscreen();
        }
      });

      // Keyboard shortcuts
      document.addEventListener('keydown', function(e) {
        // Check if editor is visible
        const editorVisible = document.getElementById('chart-editor').style.display === 'block';
        // Check if view modal is visible
        const viewModalVisible = document.getElementById('view-chart-modal').style.display === 'block';

        if (editorVisible) {
          // Editor shortcuts
          // Ctrl+S or Cmd+S to save
          if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            document.getElementById('chart-form').dispatchEvent(new Event('submit'));
          }
          // Escape to cancel
          else if (e.key === 'Escape') {
            e.preventDefault();
            hideChartEditor();
          }
          // Ctrl+Enter or Cmd+Enter to update preview
          else if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            updateChartPreview();
          }
          // F11 for fullscreen
          else if (e.key === 'F11') {
            e.preventDefault();
            document.getElementById('toggle-fullscreen').click();
          }
        } else if (viewModalVisible) {
          // View modal shortcuts
          // Escape to close modal
          if (e.key === 'Escape') {
            e.preventDefault();
            hideViewModal();
          }
          // F11 for fullscreen
          else if (e.key === 'F11') {
            e.preventDefault();
            toggleViewFullscreen();
          }
        }
      });

      // Initial preview update
      updateChartPreview();
    });

    function showChartEditor(isNew = true) {
      document.getElementById('charts-list').style.display = 'none';
      document.getElementById('chart-editor').style.display = 'block';

      if (isNew) {
        document.getElementById('editor-title').textContent = 'Create New Diagram';
        document.getElementById('chart-form').reset();
        document.getElementById('chart-id').value = '';
      }

      // Clean up any existing zoom controls
      cleanupZoomControls();

      // Update the preview with current content
      updateChartPreview();
    }

    function hideChartEditor() {
      document.getElementById('chart-editor').style.display = 'none';
      document.getElementById('charts-list').style.display = 'block';

      // Clean up any zoom controls when hiding the editor
      cleanupZoomControls();
    }

    // Centralized function to clean up zoom controls
    function cleanupZoomControls() {
      // Remove zoom controls from preview area
      const preview = document.getElementById('chart-preview');
      if (preview && preview.parentNode) {
        const existingControls = preview.parentNode.querySelectorAll('.zoom-controls');
        existingControls.forEach(control => control.remove());
      }

      // Remove zoom controls from view modal area
      const viewContainer = document.getElementById('view-chart-container');
      if (viewContainer && viewContainer.parentNode) {
        const existingControls = viewContainer.parentNode.querySelectorAll('.zoom-controls');
        existingControls.forEach(control => control.remove());
      }

      // Remove any orphaned zoom controls
      document.querySelectorAll('.zoom-controls').forEach(control => control.remove());
    }

    // Centralized function to create zoom controls
    function createZoomControls(targetContainer, zoomableElement) {
      // Create zoom controls
      const zoomControls = document.createElement('div');
      zoomControls.className = 'zoom-controls';
      zoomControls.innerHTML = `
        <button class="zoom-out" title="Zoom Out">-</button>
        <button class="zoom-reset" title="Reset Zoom">↺</button>
        <button class="zoom-in" title="Zoom In">+</button>
      `;

      // Insert zoom controls before the target container
      targetContainer.parentNode.insertBefore(zoomControls, targetContainer);

      // Make elements zoomable
      targetContainer.classList.add('zoomable-container');
      zoomableElement.classList.add('zoomable-content');

      // Set current zoom level
      let currentZoom = 1.0;

      // Get zoom control buttons
      const zoomInBtn = zoomControls.querySelector('.zoom-in');
      const zoomOutBtn = zoomControls.querySelector('.zoom-out');
      const zoomResetBtn = zoomControls.querySelector('.zoom-reset');

      // Update zoom level function
      function updateZoom() {
        zoomableElement.style.transform = `scale(${currentZoom})`;
        zoomableElement.style.transformOrigin = 'center center';

        // Update button states
        zoomInBtn.disabled = currentZoom >= 3.0;
        zoomOutBtn.disabled = currentZoom <= 0.5;

        console.log('Current zoom level:', currentZoom);
      }

      // Add event listeners
      zoomInBtn.addEventListener('click', () => {
        if (currentZoom < 3.0) {
          currentZoom += 0.25;
          updateZoom();
        }
      });

      zoomOutBtn.addEventListener('click', () => {
        if (currentZoom > 0.5) {
          currentZoom -= 0.25;
          updateZoom();
        }
      });

      zoomResetBtn.addEventListener('click', () => {
        currentZoom = 1.0;
        updateZoom();
      });

      // Initialize zoom
      updateZoom();

      return { zoomControls, updateZoom };
    }

    // Global variable to prevent overlapping preview updates
    let isUpdatingPreview = false;

    // Function to sanitize Mermaid content for problematic Unicode characters
    function sanitizeMermaidContent(content) {
      if (!content) return content;

      // Map of problematic Unicode characters to safe alternatives
      const unicodeReplacements = {
        '➕': '+',
        '➖': '-',
        '✓': 'check',
        '✗': 'x',
        '⚠': 'warn',
        '⭐': 'star',
        '🔥': 'fire',
        '💡': 'idea',
        '📊': 'chart',
        '📈': 'up',
        '📉': 'down',
        '🎯': 'target',
        '⚡': 'fast',
        '🔒': 'lock',
        '🔓': 'unlock',
        '❌': 'x',
        '⭕': 'o',
        '🟢': 'green',
        '🔴': 'red',
        '🟡': 'yellow',
        '🔵': 'blue',
        '⚫': 'black',
        '⚪': 'white'
      };

      let sanitized = content;
      let hasReplacements = false;

      // Replace problematic Unicode characters
      for (const [unicode, replacement] of Object.entries(unicodeReplacements)) {
        if (sanitized.includes(unicode)) {
          sanitized = sanitized.replace(new RegExp(unicode, 'g'), replacement);
          hasReplacements = true;
        }
      }

      // Check for other potentially problematic Unicode characters
      const problematicUnicodeRegex = /[\u2000-\u2BFF\u1F000-\u1F9FF]/g;
      const problematicChars = content.match(problematicUnicodeRegex);

      if (problematicChars && problematicChars.length > 0) {
        // Replace remaining problematic characters with safe alternatives
        sanitized = sanitized.replace(problematicUnicodeRegex, '?');
        hasReplacements = true;
      }

      return {
        content: sanitized,
        hasReplacements: hasReplacements,
        originalContent: content
      };
    }

    // Function to update syntax indicator
    function updateSyntaxIndicator(status, message = '') {
      const indicator = document.getElementById('syntax-indicator');
      indicator.className = 'syntax-indicator ' + status;

      switch(status) {
        case 'valid':
          indicator.textContent = '✓ Valid';
          indicator.title = 'Syntax is valid';
          break;
        case 'invalid':
          indicator.textContent = '✗ Invalid';
          indicator.title = message || 'Syntax error detected';
          break;
        case 'checking':
          indicator.textContent = '⟳ Checking...';
          indicator.title = 'Validating syntax...';
          break;
        case 'warning':
          indicator.textContent = '⚠ Warning';
          indicator.title = message || 'Potential issues detected';
          break;
        default:
          indicator.textContent = '';
          indicator.title = '';
      }
    }

    function updateChartPreview() {
      // Prevent overlapping updates
      if (isUpdatingPreview) {
        return;
      }

      isUpdatingPreview = true;
      const originalContent = document.getElementById('chart-content').value;
      const preview = document.getElementById('chart-preview');

      // Update syntax indicator
      updateSyntaxIndicator('checking');

      try {
        // Clear the preview div completely
        preview.innerHTML = '';

        // Clean up any existing zoom controls
        cleanupZoomControls();

        // Skip rendering if content is empty
        if (!originalContent.trim()) {
          updateSyntaxIndicator('');
          isUpdatingPreview = false; // Reset flag
          return;
        }

        // Sanitize the content for problematic Unicode characters
        const sanitizationResult = sanitizeMermaidContent(originalContent);
        const content = sanitizationResult.content;

        // Quick syntax validation using mermaid.parse
        try {
          mermaid.parse(content);
          if (sanitizationResult.hasReplacements) {
            updateSyntaxIndicator('warning', 'Some Unicode characters were replaced for compatibility');
          } else {
            updateSyntaxIndicator('valid');
          }
        } catch (parseError) {
          updateSyntaxIndicator('invalid', parseError.message || parseError);
          // Continue with rendering to show the error in preview
        }

        // Generate a unique ID for the diagram
        const id = 'mermaid-diagram-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

        // Create a div element with the mermaid class
        const mermaidDiv = document.createElement('div');
        mermaidDiv.id = id;
        mermaidDiv.className = 'mermaid';
        mermaidDiv.textContent = content;

        // Ensure the div is properly styled
        mermaidDiv.style.visibility = 'hidden'; // Hide until rendered
        preview.appendChild(mermaidDiv);

        // Use a longer timeout to ensure DOM is stable
        setTimeout(() => {
          try {
            // Check if the element is still in the DOM
            if (!document.getElementById(id)) {
              return;
            }

            // Use the init method which is more compatible
            mermaid.init(undefined, mermaidDiv);

            // Show the div and add zoom controls after rendering
            setTimeout(() => {
              if (mermaidDiv.querySelector('svg')) {
                mermaidDiv.style.visibility = 'visible';
                createZoomControls(preview, mermaidDiv);
              } else {
                // If no SVG was created, show an error
                preview.innerHTML = `<div class="error-message">
                  <strong>Unable to render diagram</strong><br>
                  Please check your Mermaid syntax.
                </div>`;
              }
              isUpdatingPreview = false; // Reset flag
            }, 100);
          } catch (error) {
            console.error('Error rendering Mermaid diagram:', error);
            preview.innerHTML = `<div class="error-message">
              <strong>Mermaid Syntax Error:</strong><br>
              ${error.message || error}<br><br>
              <strong>Common issues to check:</strong><br>
              - Indentation and whitespace<br>
              - Special characters (try using simple ASCII characters)<br>
              - Missing connections or closing brackets<br>
              - Subgraph syntax (ensure proper nesting)<br>
            </div>`;
            isUpdatingPreview = false; // Reset flag
          }
        }, 200);
      } catch (error) {
        console.error('Error setting up Mermaid diagram:', error);
        preview.innerHTML = `<div class="error-message">
          <strong>Error setting up diagram:</strong><br>
          ${error.message}<br><br>
          <strong>Common issues to check:</strong><br>
          - Indentation and whitespace<br>
          - Special characters (try using simple ASCII characters)<br>
          - Missing connections or closing brackets<br>
          - Subgraph syntax (ensure proper nesting)<br>
        </div>`;
        isUpdatingPreview = false; // Reset flag
      }
    }

    // Auto-save draft functionality
    function autoSaveDraft() {
      const chartId = document.getElementById('chart-id').value;
      const title = document.getElementById('chart-title').value;
      const content = document.getElementById('chart-content').value;
      const isPublic = document.getElementById('chart-public').checked;
      const folder = document.getElementById('chart-folder').value;
      const notes = document.getElementById('chart-notes').value;

      // Only auto-save if we have content and are editing an existing chart
      if (chartId && content.trim()) {
        const draftKey = `draft_${chartId}`;
        const draftData = {
          title: title,
          content: content,
          isPublic: isPublic,
          folder: folder,
          notes: notes,
          timestamp: Date.now()
        };

        try {
          localStorage.setItem(draftKey, JSON.stringify(draftData));
          showAutoSaveIndicator('Draft saved');
        } catch (error) {
          console.warn('Could not save draft:', error);
        }
      }
    }

    // Show auto-save indicator
    function showAutoSaveIndicator(message) {
      const indicator = document.getElementById('syntax-indicator');
      const originalContent = indicator.textContent;
      const originalClass = indicator.className;

      indicator.className = 'syntax-indicator checking';
      indicator.textContent = message;

      setTimeout(() => {
        indicator.className = originalClass;
        indicator.textContent = originalContent;
      }, 1500);
    }

    // Load draft if available
    function loadDraft(chartId) {
      const draftKey = `draft_${chartId}`;
      try {
        const draftData = localStorage.getItem(draftKey);
        if (draftData) {
          const draft = JSON.parse(draftData);
          // Check if draft is newer than 1 hour
          if (Date.now() - draft.timestamp < 3600000) {
            if (confirm('A recent draft was found. Would you like to load it?')) {
              document.getElementById('chart-title').value = draft.title;
              document.getElementById('chart-content').value = draft.content;
              document.getElementById('chart-public').checked = draft.isPublic;
              document.getElementById('chart-folder').value = draft.folder || '';
              document.getElementById('chart-notes').value = draft.notes || '';
              updateChartPreview();
            }
          }
          // Clean up old draft
          localStorage.removeItem(draftKey);
        }
      } catch (error) {
        console.warn('Could not load draft:', error);
      }
    }

    // Load folders for dropdown
    async function loadFolders() {
      try {
        const response = await fetch('/api/charts/folders/list');
        if (response.ok) {
          const data = await response.json();
          const select = document.getElementById('folder-suggestions');

          // Clear existing options except the first one
          while (select.children.length > 1) {
            select.removeChild(select.lastChild);
          }

          // Add folder options
          data.folders.forEach(folder => {
            const option = document.createElement('option');
            option.value = folder;
            option.textContent = folder;
            select.appendChild(option);
          });
        }
      } catch (error) {
        console.error('Error loading folders:', error);
      }
    }

    async function loadUserCharts() {
      try {
        // Use the getAllCharts function from charts.js to get all charts
        const charts = await getAllCharts();
        const chartsGrid = document.getElementById('charts-grid');
        const noChartsMessage = document.getElementById('no-charts-message');

        chartsGrid.innerHTML = '';

        if (charts && charts.length > 0) {
          noChartsMessage.style.display = 'none';

          charts.forEach(chart => {
            const chartCard = document.createElement('div');
            chartCard.className = 'chart-card';

            // Add folder info if present
            if (chart.folder) {
              const folderInfo = document.createElement('div');
              folderInfo.className = 'chart-folder';
              folderInfo.textContent = `📁 ${chart.folder}`;
              chartCard.appendChild(folderInfo);
            }

            // Create the card header with title
            const cardHeader = document.createElement('h4');
            cardHeader.textContent = chart.title;
            chartCard.appendChild(cardHeader);

            // Create the chart preview container with click handler for viewing
            const previewContainer = document.createElement('div');
            previewContainer.className = 'chart-preview-small';
            previewContainer.style.cursor = 'pointer';
            previewContainer.title = 'Click to view diagram';
            previewContainer.addEventListener('click', function() {
              viewChart(chart.id);
            });

            // Create a container for the rendered diagram
            const containerId = 'chart-preview-' + chart.id;
            previewContainer.id = containerId;
            chartCard.appendChild(previewContainer);

            // Sanitize the content for problematic Unicode characters
            const sanitizationResult = sanitizeMermaidContent(chart.content);
            const sanitizedContent = sanitizationResult.content;

            // Create a div with the mermaid class
            const mermaidDiv = document.createElement('div');
            mermaidDiv.className = 'mermaid';
            mermaidDiv.textContent = sanitizedContent;
            previewContainer.appendChild(mermaidDiv);

            // Use setTimeout to ensure the DOM is fully updated before rendering
            setTimeout(() => {
              try {
                // Initialize Mermaid on the new element
                mermaid.init(undefined, mermaidDiv);
              } catch (error) {
                console.error('Error rendering chart preview:', error);
                previewContainer.innerHTML = '<div class="error-message">Error rendering diagram</div>';
              }
            }, 100);

            // Create the actions container
            const actionsContainer = document.createElement('div');
            actionsContainer.className = 'chart-actions';
            actionsContainer.innerHTML = `
              <button class="nui-button small view-chart" data-id="${chart.id}">View</button>
              <button class="nui-button small edit-chart" data-id="${chart.id}">Edit</button>
              <button class="nui-button small delete-chart" data-id="${chart.id}">Delete</button>
              <button class="nui-button small share-chart" data-id="${chart.share_id}">Share</button>
            `;
            chartCard.appendChild(actionsContainer);

            // Add notes if present
            if (chart.notes) {
              const notesInfo = document.createElement('div');
              notesInfo.className = 'chart-notes';
              notesInfo.textContent = chart.notes;
              chartCard.appendChild(notesInfo);
            }

            chartsGrid.appendChild(chartCard);
          });

          // Each chart is rendered individually using the render API

          // Add event listeners to buttons
          document.querySelectorAll('.view-chart').forEach(button => {
            button.addEventListener('click', function() {
              viewChart(this.getAttribute('data-id'));
            });
          });

          document.querySelectorAll('.edit-chart').forEach(button => {
            button.addEventListener('click', function() {
              editChart(this.getAttribute('data-id'));
            });
          });

          document.querySelectorAll('.delete-chart').forEach(button => {
            button.addEventListener('click', function() {
              deleteChart(this.getAttribute('data-id'));
            });
          });

          document.querySelectorAll('.share-chart').forEach(button => {
            button.addEventListener('click', function() {
              shareChart(this.getAttribute('data-id'));
            });
          });
        } else {
          noChartsMessage.style.display = 'block';
        }
      } catch (error) {
        console.error('Error loading charts:', error);
      }
    }

    async function saveChart(e) {
      e.preventDefault();

      const chartId = document.getElementById('chart-id').value;
      const title = document.getElementById('chart-title').value;
      const content = document.getElementById('chart-content').value;
      const isPublic = document.getElementById('chart-public').checked;
      const folder = document.getElementById('chart-folder').value;
      const notes = document.getElementById('chart-notes').value;

      try {
        let url = '/api/charts';
        let method = 'POST';

        if (chartId) {
          url = `/api/charts/${chartId}`;
          method = 'PUT';
        }

        const response = await fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ title, content, isPublic, folder, notes })
        });

        if (!response.ok) {
          throw new Error('Failed to save chart');
        }

        hideChartEditor();
        loadUserCharts();
        loadFolders(); // Refresh folder list
      } catch (error) {
        console.error('Error saving chart:', error);
      }
    }

    async function editChart(chartId) {
      try {
        console.log('Editing chart with ID:', chartId);
        const chart = await getChartById(chartId);
        console.log('Chart data received:', chart);

        if (!chart) {
          alert('Chart not found');
          return;
        }

        // Make sure we're getting the correct data structure
        const chartData = chart.chart || chart;
        console.log('Chart data to use:', chartData);

        // Set the form values FIRST
        document.getElementById('chart-id').value = chartData.id;
        document.getElementById('chart-title').value = chartData.title;
        document.getElementById('chart-content').value = chartData.content;
        document.getElementById('chart-public').checked = chartData.public === 1;
        document.getElementById('chart-folder').value = chartData.folder || '';
        document.getElementById('chart-notes').value = chartData.notes || '';
        document.getElementById('editor-title').textContent = 'Edit Diagram';

        // THEN show the editor (this will call updateChartPreview with the correct content)
        showChartEditor(false);

        // Check for and load any available draft
        setTimeout(() => loadDraft(chartData.id), 100);
      } catch (error) {
        console.error('Error editing chart:', error);
      }
    }

    async function deleteChart(chartId) {
      if (!confirm('Are you sure you want to delete this diagram?')) {
        return;
      }

      try {
        const response = await fetch(`/api/charts/${chartId}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to delete chart');
        }

        loadUserCharts();
      } catch (error) {
        console.error('Error deleting chart:', error);
      }
    }

    function shareChart(shareId) {
      const shareUrl = `${window.location.origin}/share.html?id=${shareId}`;

      // Create a temporary input to copy the URL
      const tempInput = document.createElement('input');
      tempInput.value = shareUrl;
      document.body.appendChild(tempInput);
      tempInput.select();
      document.execCommand('copy');
      document.body.removeChild(tempInput);

      alert('Share URL copied to clipboard: ' + shareUrl);
    }

    // View chart in modal
    async function viewChart(chartId) {
      try {
        console.log('Viewing chart with ID:', chartId);
        // Get the chart data
        const chart = await getChartById(chartId);
        console.log('Chart data received for view:', chart);

        if (!chart) {
          alert('Chart not found');
          return;
        }

        // Make sure we're getting the correct data structure
        const chartData = chart.chart || chart;
        console.log('Chart data to use for view:', chartData);

        // Set the chart title
        document.getElementById('view-chart-title').textContent = chartData.title;

        // Set data attributes for the action buttons
        document.getElementById('view-edit-chart').setAttribute('data-id', chartData.id);
        document.getElementById('view-delete-chart').setAttribute('data-id', chartData.id);
        document.getElementById('view-share-chart').setAttribute('data-id', chartData.share_id);

        // Show the modal first to ensure the container is visible
        document.getElementById('view-chart-modal').style.display = 'block';

        // Get the container
        const container = document.getElementById('view-chart-container');
        container.innerHTML = '';

        // Clean up any existing zoom controls
        cleanupZoomControls();

        // Sanitize the content for problematic Unicode characters
        const sanitizationResult = sanitizeMermaidContent(chartData.content);
        const sanitizedContent = sanitizationResult.content;

        // Create a div with the mermaid class
        const mermaidDiv = document.createElement('div');
        mermaidDiv.className = 'mermaid';
        mermaidDiv.textContent = sanitizedContent;
        container.appendChild(mermaidDiv);

        // Use setTimeout to ensure the DOM is fully updated before rendering
        setTimeout(() => {
          try {
            // Initialize Mermaid on the new element
            mermaid.init(undefined, mermaidDiv);

            // Add zoom controls using centralized function
            createZoomControls(container, mermaidDiv);
          } catch (error) {
            console.error('Error rendering diagram in modal:', error);
            container.innerHTML = `<div class="error-message">
              <strong>Error rendering diagram:</strong><br>
              ${error.message || 'Unknown error'}<br><br>
              <strong>Common issues to check:</strong><br>
              - Indentation and whitespace<br>
              - Special characters (try using simple ASCII characters)<br>
              - Missing connections or closing brackets<br>
              - Subgraph syntax (ensure proper nesting)<br>
            </div>`;
          }
        }, 100);
      } catch (error) {
        console.error('Error viewing chart:', error);
        alert('Error viewing chart: ' + error.message);
      }
    }

    // Toggle fullscreen view
    function toggleViewFullscreen() {
      const modal = document.getElementById('view-chart-modal');
      const button = document.getElementById('view-fullscreen-chart');

      if (modal.classList.contains('fullscreen')) {
        // Exit fullscreen
        modal.classList.remove('fullscreen');
        button.textContent = '⛶ Fullscreen';
        document.body.style.overflow = '';
      } else {
        // Enter fullscreen
        modal.classList.add('fullscreen');
        button.textContent = '⛶ Exit Fullscreen';
        document.body.style.overflow = 'hidden';
      }
    }

    // Hide the view modal
    function hideViewModal() {
      const modal = document.getElementById('view-chart-modal');
      modal.style.display = 'none';

      // Exit fullscreen if active
      if (modal.classList.contains('fullscreen')) {
        modal.classList.remove('fullscreen');
        document.getElementById('view-fullscreen-chart').textContent = '⛶ Fullscreen';
        document.body.style.overflow = '';
      }

      // Clean up any zoom controls when hiding the modal
      cleanupZoomControls();
    }

    // Function to fetch current user from session
    async function fetchCurrentUser() {
      try {
        const response = await fetch('/api/users/me');
        if (!response.ok) {
          if (response.status === 401) {
            // Not authenticated
            return null;
          }
          throw new Error('Failed to fetch current user');
        }

        const data = await response.json();
        return data.user;
      } catch (error) {
        console.error('Error fetching current user:', error);
        return null;
      }
    }
  </script>
</body>
</html>
